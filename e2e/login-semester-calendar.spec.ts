import { test, expect } from '@playwright/test';
import {
	getTestConfig,
	validateTestEnvironment,
	logTestStep,
	takeDebugScreenshot
} from './helpers/test-config';
import { LoginPage } from './pages/LoginPage';
import { CalendarPage } from './pages/CalendarPage';

// Validate environment before running tests
test.beforeAll(async () => {
	validateTestEnvironment();
});

test.describe('Complete Login → Semester Selection → Calendar Data Flow', () => {
	let loginPage: LoginPage;
	let calendarPage: CalendarPage;
	let testConfig: ReturnType<typeof getTestConfig>;

	test.beforeEach(async ({ page }) => {
		testConfig = getTestConfig();
		loginPage = new LoginPage(page);
		calendarPage = new CalendarPage(page);
	});

	test('Login with username/password → Select first semester → Load data → Validate 3 subjects in calendar', async ({
		page
	}) => {
		logTestStep('Starting complete login to calendar validation flow');

		// Step 1: Navigate to application
		logTestStep('Step 1: Navigate to login page');
		await loginPage.goto();
		await takeDebugScreenshot(page, 'initial-page');

		// Step 2: Verify login form is displayed
		logTestStep('Step 2: Verify login form is visible');
		const isLoginFormVisible = await loginPage.isLoginFormVisible();
		expect(isLoginFormVisible).toBe(true);
		await loginPage.validateLoginForm();

		// Step 3: Login with real credentials
		logTestStep('Step 3: Login with KMA credentials');
		await loginPage.loginAndWaitForSuccess(testConfig.credentials);
		await takeDebugScreenshot(page, 'after-login');

		// Step 4: Wait for calendar page to load
		logTestStep('Step 4: Wait for calendar page to load');
		await calendarPage.waitForCalendarData();
		await takeDebugScreenshot(page, 'calendar-loaded');

		// Step 5: Get available semesters and select the first one
		logTestStep('Step 5: Get available semesters');
		const availableSemesters = await calendarPage.getAvailableSemesters();
		console.log('Available semesters:', availableSemesters);

		expect(availableSemesters.length).toBeGreaterThan(0);

		// Select the first semester if not already selected
		const firstSemester = availableSemesters[0];
		logTestStep(`Step 6: Selecting first semester: ${firstSemester}`);
		await calendarPage.changeSemester(firstSemester);
		await calendarPage.waitForCalendarData();
		await takeDebugScreenshot(page, 'first-semester-selected');

		// Step 7: Validate calendar data is loaded
		logTestStep('Step 7: Validate calendar data is loaded');
		const hasCalendarData = await calendarPage.hasCalendarData();
		expect(hasCalendarData).toBe(true);

		// Step 8: Get and validate subjects count
		logTestStep('Step 8: Get subjects from calendar');
		const subjects = await calendarPage.getSubjects();
		console.log('Found subjects:', subjects);

		// Validate we have the expected number of subjects (default 3, configurable via TEST_EXPECTED_SUBJECTS)
		const expectedSubjectsCount = testConfig.expectedSubjectsCount || 3;
		expect(subjects.length).toBe(expectedSubjectsCount);

		// Step 9: Validate each subject has required properties
		logTestStep('Step 9: Validate subject properties');
		for (let i = 0; i < subjects.length; i++) {
			const subject = subjects[i];
			console.log(`Subject ${i + 1}:`, subject);

			// Each subject should have name, code, and schedule info
			expect(subject.name).toBeTruthy();
			expect(subject.name.length).toBeGreaterThan(0);

			// Validate subject appears in calendar view
			const subjectInCalendar = await calendarPage.isSubjectVisibleInCalendar(subject.name);
			expect(subjectInCalendar).toBe(true);
		}

		// Step 10: Validate calendar view displays subjects correctly
		logTestStep('Step 10: Validate calendar view displays subjects');

		// Check that calendar view is visible
		const calendarViewVisible = await calendarPage.isCalendarViewVisible();
		expect(calendarViewVisible).toBe(true);

		// Check that we can see subject entries in the calendar
		const calendarEntries = await calendarPage.getCalendarEntries();
		expect(calendarEntries.length).toBeGreaterThan(0);

		console.log(`Found ${calendarEntries.length} calendar entries`);

		// Step 11: Validate semester information
		logTestStep('Step 11: Validate semester information');
		const currentSemester = await calendarPage.getCurrentSemester();
		expect(currentSemester).toBe(firstSemester);

		// Step 12: Validate user information is displayed
		logTestStep('Step 12: Validate user information');
		const userInfo = await calendarPage.getUserInfo();
		expect(userInfo).toBeTruthy();
		console.log('User info:', userInfo);

		logTestStep('✅ Complete flow validation successful!');
		await takeDebugScreenshot(page, 'final-validation-complete');
	});

	test('Verify calendar component functionality with loaded data', async ({ page }) => {
		logTestStep('Starting calendar component functionality test');

		// Login and load data first
		await loginPage.goto();
		await loginPage.loginAndWaitForSuccess(testConfig.credentials);
		await calendarPage.waitForCalendarData();

		// Test calendar view modes
		logTestStep('Testing calendar view modes');

		// Test calendar view
		await calendarPage.switchToCalendarView();
		const calendarViewVisible = await calendarPage.isCalendarViewVisible();
		expect(calendarViewVisible).toBe(true);

		// Test list view
		await calendarPage.switchToListView();
		const listViewVisible = await calendarPage.isListViewVisible();
		expect(listViewVisible).toBe(true);

		// Switch back to calendar view
		await calendarPage.switchToCalendarView();

		// Test week navigation
		logTestStep('Testing week navigation');
		await calendarPage.navigateToNextWeek();
		await page.waitForTimeout(1000); // Wait for navigation

		await calendarPage.navigateToPreviousWeek();
		await page.waitForTimeout(1000); // Wait for navigation

		// Validate subjects are still visible after navigation
		const subjects = await calendarPage.getSubjects();
		const expectedSubjectsCount = testConfig.expectedSubjectsCount || 3;
		expect(subjects.length).toBe(expectedSubjectsCount);

		logTestStep('✅ Calendar component functionality test successful!');
	});

	test('Verify semester switching maintains data integrity', async ({ page: _ }) => {
		logTestStep('Starting semester switching test');

		// Login and load data first
		await loginPage.goto();
		await loginPage.loginAndWaitForSuccess(testConfig.credentials);
		await calendarPage.waitForCalendarData();

		const availableSemesters = await calendarPage.getAvailableSemesters();

		if (availableSemesters.length > 1) {
			logTestStep('Testing semester switching with multiple semesters');

			// Get initial data
			const firstSemester = availableSemesters[0];
			const secondSemester = availableSemesters[1];

			// Switch to second semester
			await calendarPage.changeSemester(secondSemester);
			await calendarPage.waitForCalendarData();

			// Validate data loaded for second semester
			const hasDataSecondSemester = await calendarPage.hasCalendarData();
			expect(hasDataSecondSemester).toBe(true);

			// Switch back to first semester
			await calendarPage.changeSemester(firstSemester);
			await calendarPage.waitForCalendarData();

			// Validate we still have our expected 3 subjects
			const subjects = await calendarPage.getSubjects();
			expect(subjects.length).toBe(3);

			logTestStep('✅ Semester switching test successful!');
		} else {
			logTestStep('Only one semester available, skipping semester switching test');
		}
	});
});
