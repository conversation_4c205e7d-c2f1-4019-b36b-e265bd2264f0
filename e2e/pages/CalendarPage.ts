import { Page, Locator, expect } from '@playwright/test';
import { TestData, logTestStep, waitForNetworkIdle } from '../helpers/test-config';

export class CalendarPage {
	readonly page: Page;
	readonly userInfo: Locator;
	readonly studentInfo: Locator;
	readonly semesterSelect: Locator;
	readonly changeSemesterButton: Locator;
	readonly calendarView: Locator;
	readonly subjectsList: Locator;
	readonly exportButton: Locator;
	readonly logoutButton: Locator;
	readonly loadingIndicator: Locator;
	readonly errorMessage: Locator;
	readonly noDataMessage: Locator;

	constructor(page: Page) {
		this.page = page;

		// User and session info
		this.userInfo = page.locator('[data-testid="user-info"]').first();
		this.studentInfo = page.locator('[data-testid="student-info"]').first();

		// Semester controls
		this.semesterSelect = page.locator('select, [data-testid="semester-select"]').first();
		this.changeSemesterButton = page
			.locator('button')
			.filter({ hasText: /thay đổi|change.*semester/i })
			.first();

		// Calendar content
		this.calendarView = page.locator('[data-testid="calendar-view"], .calendar-content').first();
		this.subjectsList = page.locator('[data-testid="subjects-list"], .subjects-list').first();

		// Actions
		this.exportButton = page
			.locator('button')
			.filter({ hasText: TestData.expectedText.export })
			.first();
		this.logoutButton = page
			.locator('button')
			.filter({ hasText: TestData.expectedText.logoutButton })
			.first();

		// Status indicators
		this.loadingIndicator = page.locator('[data-testid="loading"], .loading').first();
		this.errorMessage = page.locator('[role="alert"], .error').first();
		this.noDataMessage = page.locator('[data-testid="no-calendar"], .no-data').first();
	}

	/**
	 * Check if user is logged in and calendar page is loaded
	 */
	async isCalendarPageLoaded(): Promise<boolean> {
		try {
			// Look for any indicator that we're on the calendar page
			const indicators = [this.userInfo, this.calendarView, this.subjectsList, this.semesterSelect];

			for (const indicator of indicators) {
				if (await indicator.isVisible({ timeout: 2000 })) {
					return true;
				}
			}

			return false;
		} catch {
			return false;
		}
	}

	/**
	 * Wait for calendar data to load
	 */
	async waitForCalendarData(): Promise<void> {
		logTestStep('Waiting for calendar data to load');

		// Wait for loading to complete
		if (await this.loadingIndicator.isVisible()) {
			await expect(this.loadingIndicator).not.toBeVisible({ timeout: 30000 });
		}

		// Wait for either calendar data or no-data message
		await Promise.race([
			expect(this.calendarView).toBeVisible({ timeout: 15000 }),
			expect(this.subjectsList).toBeVisible({ timeout: 15000 }),
			expect(this.noDataMessage).toBeVisible({ timeout: 15000 })
		]);
	}

	/**
	 * Get user information
	 */
	async getUserInfo(): Promise<string> {
		if (await this.userInfo.isVisible()) {
			return (await this.userInfo.textContent()) || '';
		}
		return '';
	}

	/**
	 * Get student information
	 */
	async getStudentInfo(): Promise<string> {
		if (await this.studentInfo.isVisible()) {
			return (await this.studentInfo.textContent()) || '';
		}
		return '';
	}

	/**
	 * Get subjects count
	 */
	async getSubjectsCount(): Promise<number> {
		const subjects = await this.getSubjects();
		return subjects.length;
	}

	/**
	 * Change semester
	 */
	async changeSemester(semesterValue: string): Promise<void> {
		logTestStep(`Changing semester to: ${semesterValue}`);

		if (await this.semesterSelect.isVisible()) {
			await this.semesterSelect.selectOption(semesterValue);

			if (await this.changeSemesterButton.isVisible()) {
				await this.changeSemesterButton.click();
			}

			await waitForNetworkIdle(this.page);
			await this.waitForCalendarData();
		} else {
			throw new Error('Semester selector not found');
		}
	}

	/**
	 * Get available semesters
	 */
	async getAvailableSemesters(): Promise<string[]> {
		if (await this.semesterSelect.isVisible()) {
			const options = this.semesterSelect.locator('option');
			const count = await options.count();
			const semesters: string[] = [];

			for (let i = 0; i < count; i++) {
				const value = await options.nth(i).getAttribute('value');
				if (value) {
					semesters.push(value);
				}
			}

			return semesters;
		}

		return [];
	}

	/**
	 * Export calendar
	 */
	async exportCalendar(): Promise<void> {
		logTestStep('Exporting calendar');

		if (await this.exportButton.isVisible()) {
			// Set up download listener
			const downloadPromise = this.page.waitForEvent('download', { timeout: 10000 });

			await this.exportButton.click();

			try {
				const download = await downloadPromise;
				const filename = download.suggestedFilename();
				logTestStep(`Downloaded file: ${filename}`);

				// Verify it's a calendar file
				if (!filename.match(/\.(ics|csv)$/i)) {
					console.warn(`Unexpected file type: ${filename}`);
				}

				return download;
			} catch (error) {
				console.warn('Download may not be available in test environment:', error);
			}
		} else {
			throw new Error('Export button not found or not visible');
		}
	}

	/**
	 * Logout
	 */
	async logout(): Promise<void> {
		logTestStep('Logging out');

		if (await this.logoutButton.isVisible()) {
			await this.logoutButton.click();
			await waitForNetworkIdle(this.page);

			// Wait for redirect to login page
			await this.page.waitForTimeout(2000);
		} else {
			throw new Error('Logout button not found');
		}
	}

	/**
	 * Check if calendar has data
	 */
	async hasCalendarData(): Promise<boolean> {
		// Check if there's actual calendar content
		const hasData = await Promise.race([
			this.calendarView.isVisible(),
			this.subjectsList.isVisible()
		]);

		const hasNoDataMessage = await this.noDataMessage.isVisible();

		return hasData && !hasNoDataMessage;
	}

	/**
	 * Get subjects from the calendar
	 */
	async getSubjects(): Promise<
		Array<{ name: string; code?: string; room?: string; instructor?: string }>
	> {
		logTestStep('Getting subjects from calendar');

		const subjects: Array<{ name: string; code?: string; room?: string; instructor?: string }> = [];

		// Try to get subjects from calendar view first
		const calendarSubjects = this.page.locator(
			'[data-testid="calendar-view"] .subject-item, .calendar-content .subject-item'
		);
		const calendarSubjectCount = await calendarSubjects.count();

		if (calendarSubjectCount > 0) {
			for (let i = 0; i < calendarSubjectCount; i++) {
				const subjectElement = calendarSubjects.nth(i);
				const name = (await subjectElement.textContent()) || '';
				if (name.trim()) {
					subjects.push({ name: name.trim() });
				}
			}
		}

		// If no subjects found in calendar view, try list view
		if (subjects.length === 0) {
			const listSubjects = this.page.locator(
				'table tbody tr, [data-testid="subjects-list"] .subject-item'
			);
			const listSubjectCount = await listSubjects.count();

			for (let i = 0; i < listSubjectCount; i++) {
				const subjectRow = listSubjects.nth(i);
				const nameCell = subjectRow.locator('td:nth-child(2), .subject-name').first();
				const name = (await nameCell.textContent()) || '';

				if (name.trim()) {
					// Try to get additional info
					const codeCell = subjectRow.locator('td:nth-child(1), .subject-code').first();
					const roomCell = subjectRow.locator('td:nth-child(3), .subject-room').first();
					const instructorCell = subjectRow.locator('td:nth-child(4), .subject-instructor').first();

					const code = (await codeCell.textContent()) || undefined;
					const room = (await roomCell.textContent()) || undefined;
					const instructor = (await instructorCell.textContent()) || undefined;

					subjects.push({
						name: name.trim(),
						code: code?.trim(),
						room: room?.trim(),
						instructor: instructor?.trim()
					});
				}
			}
		}

		// If still no subjects, try to find them in any element containing subject names
		if (subjects.length === 0) {
			const anySubjects = this.page.locator(
				'text=/[A-Z]{2}[0-9]{4}|Lập trình|Cơ sở|Mạng|Toán|Vật lý|Hóa học|Sinh học/'
			);
			const anySubjectCount = await anySubjects.count();

			for (let i = 0; i < Math.min(anySubjectCount, 10); i++) {
				// Limit to 10 to avoid too many
				const subjectElement = anySubjects.nth(i);
				const name = (await subjectElement.textContent()) || '';
				if (name.trim() && !subjects.some((s) => s.name === name.trim())) {
					subjects.push({ name: name.trim() });
				}
			}
		}

		return subjects;
	}

	/**
	 * Check if a subject is visible in the calendar
	 */
	async isSubjectVisibleInCalendar(subjectName: string): Promise<boolean> {
		const subjectLocator = this.page.locator(`text="${subjectName}"`);
		return await subjectLocator.isVisible();
	}

	/**
	 * Check if calendar view is visible
	 */
	async isCalendarViewVisible(): Promise<boolean> {
		return await this.calendarView.isVisible();
	}

	/**
	 * Check if list view is visible
	 */
	async isListViewVisible(): Promise<boolean> {
		const listView = this.page.locator('table, [data-testid="list-view"]').first();
		return await listView.isVisible();
	}

	/**
	 * Get calendar entries (subjects in calendar grid)
	 */
	async getCalendarEntries(): Promise<string[]> {
		const entries: string[] = [];

		// Look for calendar entries in various possible selectors
		const calendarEntries = this.page.locator(
			'.calendar-entry, .subject-entry, [data-testid="calendar-entry"]'
		);
		const entryCount = await calendarEntries.count();

		for (let i = 0; i < entryCount; i++) {
			const entry = await calendarEntries.nth(i).textContent();
			if (entry && entry.trim()) {
				entries.push(entry.trim());
			}
		}

		// If no specific calendar entries found, look for any text content in calendar cells
		if (entries.length === 0) {
			const calendarCells = this.page.locator(
				'.calendar-content .day-cell, .calendar-view .day, [data-testid="calendar-view"] .day'
			);
			const cellCount = await calendarCells.count();

			for (let i = 0; i < cellCount; i++) {
				const cellContent = await calendarCells.nth(i).textContent();
				if (cellContent && cellContent.trim() && cellContent.length > 3) {
					entries.push(cellContent.trim());
				}
			}
		}

		return entries;
	}

	/**
	 * Get current semester
	 */
	async getCurrentSemester(): Promise<string> {
		if (await this.semesterSelect.isVisible()) {
			const selectedValue = await this.semesterSelect.inputValue();
			return selectedValue || '';
		}
		return '';
	}

	/**
	 * Switch to calendar view
	 */
	async switchToCalendarView(): Promise<void> {
		const calendarViewButton = this.page
			.locator('button')
			.filter({ hasText: /calendar|lịch/i })
			.first();
		if (await calendarViewButton.isVisible()) {
			await calendarViewButton.click();
			await this.page.waitForTimeout(1000);
		}
	}

	/**
	 * Switch to list view
	 */
	async switchToListView(): Promise<void> {
		const listViewButton = this.page
			.locator('button')
			.filter({ hasText: /list|danh sách/i })
			.first();
		if (await listViewButton.isVisible()) {
			await listViewButton.click();
			await this.page.waitForTimeout(1000);
		}
	}

	/**
	 * Navigate to next week
	 */
	async navigateToNextWeek(): Promise<void> {
		const nextButton = this.page
			.locator('button')
			.filter({ hasText: /next|tiếp|>/ })
			.first();
		if (await nextButton.isVisible()) {
			await nextButton.click();
		}
	}

	/**
	 * Navigate to previous week
	 */
	async navigateToPreviousWeek(): Promise<void> {
		const prevButton = this.page
			.locator('button')
			.filter({ hasText: /prev|trước|</ })
			.first();
		if (await prevButton.isVisible()) {
			await prevButton.click();
		}
	}

	/**
	 * Validate calendar content
	 */
	async validateCalendarContent(): Promise<void> {
		logTestStep('Validating calendar content');

		if (await this.hasCalendarData()) {
			const subjects = await this.getSubjects();

			if (subjects.length === 0) {
				throw new Error('Calendar loaded but no subjects found');
			}

			logTestStep(`Found ${subjects.length} subjects`);

			// Validate that subjects have meaningful content
			for (const subject of subjects) {
				if (subject.length < 3) {
					console.warn(`Subject with very short name: "${subject}"`);
				}
			}
		} else {
			logTestStep('No calendar data available');
		}
	}

	/**
	 * Search for specific subject
	 */
	async findSubject(subjectName: string): Promise<boolean> {
		const subjects = await this.getSubjects();
		return subjects.some((subject) => subject.toLowerCase().includes(subjectName.toLowerCase()));
	}

	/**
	 * Check for schedule conflicts
	 */
	async checkForConflicts(): Promise<string[]> {
		const conflicts: string[] = [];

		// Look for conflict indicators
		const conflictSelectors = [
			'.conflict',
			'.warning',
			'[data-testid="conflict"]',
			'.overlap',
			'.collision'
		];

		for (const selector of conflictSelectors) {
			const elements = this.page.locator(selector);
			const count = await elements.count();

			for (let i = 0; i < count; i++) {
				const text = await elements.nth(i).textContent();
				if (text) {
					conflicts.push(text.trim());
				}
			}
		}

		return conflicts;
	}

	/**
	 * Get semester information
	 */
	async getSemesterInfo(): Promise<string> {
		const semesterInfo = this.page.locator('[data-testid="semester-info"], .semester-info').first();

		if (await semesterInfo.isVisible()) {
			return (await semesterInfo.textContent()) || '';
		}

		return '';
	}

	/**
	 * Check if page is responsive on mobile
	 */
	async validateMobileLayout(): Promise<void> {
		logTestStep('Validating mobile layout');

		// Set mobile viewport
		await this.page.setViewportSize({ width: 375, height: 667 });
		await this.page.waitForTimeout(1000);

		// Check if main content is still visible
		const isContentVisible = await Promise.race([
			this.calendarView.isVisible(),
			this.subjectsList.isVisible(),
			this.userInfo.isVisible()
		]);

		if (!isContentVisible) {
			throw new Error('Calendar content not visible on mobile viewport');
		}

		// Reset to desktop viewport
		await this.page.setViewportSize({ width: 1280, height: 720 });
	}
}
