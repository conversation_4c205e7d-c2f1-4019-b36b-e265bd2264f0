const dotenv = require('dotenv')

module.exports = async () => {
  // Load environment variables from .env file
  dotenv.config()

  // Set up global test configuration
  process.env.NODE_ENV = 'test'
  
  // Configure timeouts for real account tests
  if (process.env.TEST_USERNAME && process.env.TEST_PASSWORD) {
    console.log('🔧 Real account testing enabled')
    console.log('📝 Test credentials configured for:', process.env.TEST_USERNAME)
    
    // Set longer timeout for real account tests
    process.env.JEST_TIMEOUT = '60000'
    
    // Configure test environment
    process.env.TEST_ENVIRONMENT = 'real'
  } else {
    console.log('⚠️  Real account testing disabled - no credentials found')
    console.log('💡 To enable real account testing:')
    console.log('   1. Copy .env.example to .env')
    console.log('   2. Add your KMA credentials to .env file')
    console.log('   3. Run tests again')
    
    process.env.TEST_ENVIRONMENT = 'mock'
  }

  // Configure network settings for real tests
  if (process.env.TEST_ENVIRONMENT === 'real') {
    // Set reasonable timeouts for network requests
    process.env.FETCH_TIMEOUT = '30000'
    process.env.LOGIN_TIMEOUT = '45000'
    process.env.CALENDAR_TIMEOUT = '30000'
  }

  // Log test configuration
  console.log('🧪 Jest Global Setup Complete')
  console.log('Environment:', process.env.TEST_ENVIRONMENT)
  console.log('Timeout:', process.env.JEST_TIMEOUT || '30000', 'ms')
}
