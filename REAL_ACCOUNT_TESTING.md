# Real Account Testing Setup Guide

## Quick Setup

### 1. Configure Environment Variables

Copy the example environment file and add your KMA credentials:

```bash
cp .env.example .env
```

Edit `.env` and add your credentials:

```bash
# Required for real account testing
TEST_USERNAME=your_kma_username
TEST_PASSWORD=your_kma_password

# Optional: Alternative credentials for testing multiple accounts
TEST_USERNAME_2=another_username
TEST_PASSWORD_2=another_password

# Optional: Expected test data for validation
TEST_SEMESTER=1_2025_2026
TEST_EXPECTED_SUBJECTS=3
```

### 2. Run Tests

```bash
# Run all real account tests
npm run test:real

# Run with watch mode for development
npm run test:real:watch

# Run specific test files
npm test -- --testPathPattern="user.test.ts"
npm test -- --testPathPattern="calendar.test.ts"
npm test -- --testPathPattern="storage.test.ts"
npm test -- --testPathPattern="useCalendarData-real.test.tsx"
```

## What Gets Tested

### ✅ Authentication Functions
- Login with real KMA credentials
- Handle invalid credentials
- Network error scenarios
- Token validation and parsing

### ✅ Calendar Data Processing
- Fetch real calendar data from KMA
- Parse HTML responses
- Extract student information
- Process semester data
- Handle semester changes

### ✅ Data Storage
- Save/load real calendar data
- Handle large datasets efficiently
- Performance testing
- Data corruption recovery

### ✅ Integration Flows
- Complete login-to-calendar flow
- Manual data processing
- Semester management
- Error handling scenarios

## Expected Output

When tests run successfully, you'll see output like:

```
✅ Real account login successful
✅ Calendar data processing successful
Student: Nguyễn Văn A - CT050101
Calendar subjects: 8
Available semesters: 3
Current semester: 1_2025_2026

✅ Real data storage and retrieval successful
Save time: 15ms
Load time: 8ms
Data size: 12543 characters
```

## Troubleshooting

### Tests are being skipped
```
⚠️ Skipping real account test - no credentials configured
```
**Solution**: Make sure `TEST_USERNAME` and `TEST_PASSWORD` are set in your `.env` file.

### Authentication failures
```
❌ Real account login failed: Invalid credentials
```
**Solutions**:
- Verify your KMA username and password are correct
- Check if your account is locked or suspended
- Ensure your password hasn't expired
- Try logging in manually to KMA website first

### Network timeouts
```
❌ Operation timed out after 30000ms
```
**Solutions**:
- Check your internet connection
- Verify KMA systems are available
- Try running tests during off-peak hours
- Increase timeout in test files if needed

### Rate limiting
```
❌ Too many requests
```
**Solutions**:
- Wait a few minutes before running tests again
- Use `npm run test:real:watch` for development (runs tests one at a time)
- Consider using a dedicated test account

## Security Best Practices

### ✅ DO
- Use `.env` file for local testing
- Use environment variables in CI/CD
- Use dedicated test accounts when possible
- Regularly rotate test credentials
- Keep credentials secure and private

### ❌ DON'T
- Commit real credentials to version control
- Share credentials in chat or email
- Use production accounts for testing
- Leave credentials in code comments
- Use the same password for multiple accounts

## CI/CD Setup

For automated testing in CI/CD pipelines:

```yaml
# GitHub Actions example
env:
  TEST_USERNAME: ${{ secrets.TEST_USERNAME }}
  TEST_PASSWORD: ${{ secrets.TEST_PASSWORD }}

steps:
  - name: Run real account tests
    run: npm run test:real
```

## Performance Notes

- Real account tests take longer (30-60 seconds each)
- Tests run sequentially to avoid rate limiting
- Network conditions affect test duration
- Large calendar datasets may take extra time to process

## Getting Help

If you encounter issues:

1. Check the full test output for detailed error messages
2. Verify your credentials work on the KMA website
3. Try running individual test files to isolate issues
4. Check network connectivity and KMA system status
5. Review the troubleshooting section above

For more detailed information, see [TESTING.md](./TESTING.md).
