import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { AppProvider } from '@/contexts/AppContext';
import { useCalendarData } from '@/hooks/use-calendar-data';
import {
	getTestCredentials,
	skipIfNoRealAccount,
	validateTestResponse,
	waitForOperation,
	TestCredentials
} from '../utils/test-utils';

// Mock notifications to avoid UI dependencies
jest.mock('@/hooks/use-notifications', () => ({
	useNotifications: () => ({
		showSuccess: jest.fn(),
		showError: jest.fn(),
		showInfo: jest.fn(),
		showWarning: jest.fn()
	})
}));

// Mock storage functions
jest.mock('@/lib/ts/storage', () => ({
	saveData: jest.fn(),
	clearData: jest.fn(),
	loadData: jest.fn()
}));

describe('useCalendarData Hook - Real Account Integration', () => {
	const wrapper = ({ children }: { children: React.ReactNode }) => (
		<AppProvider>{children}</AppProvider>
	);

	beforeEach(() => {
		jest.clearAllMocks();
		if (skipIfNoRealAccount()) {
			return;
		}

		// Reset mocks
		const { saveData } = require('@/lib/ts/storage');
		if (saveData && saveData.mockClear) {
			saveData.mockClear();
		}
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	describe('Real Account Login Flow', () => {
		it('should successfully login with real credentials and process data', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			const credentials = getTestCredentials();
			jest.setTimeout(90000);

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			expect(result.current.isProcessing).toBe(false);
			expect(typeof result.current.loginWithCredentials).toBe('function');

			let loginResult: any;

			try {
				await act(async () => {
					loginResult = await waitForOperation(
						() => result.current.loginWithCredentials(credentials.username, credentials.password),
						60000
					);
				});

				expect(loginResult).toBeDefined();
				expect(loginResult.success).toBe(true);
				expect(loginResult.data).toBeDefined();

				// Validate returned data structure
				expect(loginResult.data).toHaveProperty('calendar');
				expect(loginResult.data).toHaveProperty('student');
				expect(loginResult.data).toHaveProperty('mainForm');
				expect(loginResult.data).toHaveProperty('semesters');

				// Validate calendar data
				const { calendar } = loginResult.data;
				expect(calendar).toBeDefined();
				if (calendar && calendar.data_subject) {
					expect(Array.isArray(calendar.data_subject)).toBe(true);
					console.log('✅ Calendar subjects found:', calendar.data_subject.length);
				}

				// Validate student data
				const { student } = loginResult.data;
				expect(student).toBeDefined();
				expect(typeof student).toBe('string');
				console.log('✅ Student name:', student);

				// Validate semesters data
				const { semesters } = loginResult.data;
				expect(semesters).toBeDefined();
				expect(semesters).toHaveProperty('semesters');
				expect(semesters).toHaveProperty('currentSemester');
				expect(Array.isArray(semesters.semesters)).toBe(true);
				console.log('✅ Available semesters:', semesters.semesters.length);
				console.log('✅ Current semester:', semesters.currentSemester);

				// Verify storage was called
				const { saveData } = require('@/lib/ts/storage');
				expect(saveData).toHaveBeenCalledWith({
					signInToken: expect.any(String),
					mainForm: expect.any(Object),
					semesters: expect.any(Object),
					calendar: expect.any(Object),
					student: expect.any(String)
				});

				console.log('✅ Real account login and data processing successful');
			} catch (error) {
				console.error('❌ Real account login failed:', error);
				throw error;
			}
		}, 90000);

		it('should handle invalid credentials gracefully', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			jest.setTimeout(30000);

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			let loginResult: any;

			try {
				await act(async () => {
					loginResult = await result.current.loginWithCredentials(
						'invalid_user_12345',
						'invalid_pass_12345'
					);
				});

				expect(loginResult).toBeDefined();
				expect(loginResult.success).toBe(false);
				expect(loginResult.error).toBeDefined();
				expect(typeof loginResult.error).toBe('string');

				console.log('✅ Invalid credentials properly rejected');
				console.log('Error message:', loginResult.error);
			} catch (error) {
				// Network errors are also acceptable for invalid credentials
				console.log('✅ Invalid credentials caused expected error:', error);
			}
		}, 30000);

		it('should handle network errors during login', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			const credentials = getTestCredentials();
			jest.setTimeout(15000);

			// Mock fetch to simulate network error
			const originalFetch = global.fetch;
			global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			try {
				let loginResult: any;

				await act(async () => {
					loginResult = await result.current.loginWithCredentials(
						credentials.username,
						credentials.password
					);
				});

				expect(loginResult).toBeDefined();
				expect(loginResult.success).toBe(false);
				expect(loginResult.error).toContain('Network error');

				console.log('✅ Network error handled correctly');
			} finally {
				global.fetch = originalFetch;
			}
		}, 15000);
	});

	describe('Manual Data Processing', () => {
		it('should process manual calendar data', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			// First get real data to use as manual input
			const credentials = getTestCredentials();
			jest.setTimeout(60000);

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			try {
				// Login to get real data
				let loginResult: any;
				await act(async () => {
					loginResult = await result.current.loginWithCredentials(
						credentials.username,
						credentials.password
					);
				});

				if (loginResult.success) {
					// Use the raw HTML as manual data
					const { login } = await import('@/lib/ts/user');
					const { fetchCalendarWithGet } = await import('@/lib/ts/calendar');

					const signInToken = await login(credentials.username, credentials.password);
					const rawHtml = await fetchCalendarWithGet(signInToken);

					// Test manual data processing
					let manualResult: any;
					await act(async () => {
						manualResult = await result.current.processManualData(rawHtml);
					});

					expect(manualResult).toBeDefined();
					expect(manualResult.success).toBe(true);
					expect(manualResult.data).toBeDefined();

					console.log('✅ Manual data processing successful');
				}
			} catch (error) {
				console.error('❌ Manual data processing failed:', error);
				throw error;
			}
		}, 60000);
	});

	describe('Semester Management', () => {
		it('should handle semester changes with real data', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			const credentials = getTestCredentials();
			jest.setTimeout(90000);

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			try {
				// First login
				let loginResult: any;
				await act(async () => {
					loginResult = await result.current.loginWithCredentials(
						credentials.username,
						credentials.password
					);
				});

				if (loginResult.success && loginResult.data.semesters) {
					const { semesters } = loginResult.data;

					if (semesters.semesters.length > 1) {
						// Find alternative semester
						const alternativeSemester = semesters.semesters.find(
							(s: any) => s.value !== semesters.currentSemester
						);

						if (alternativeSemester) {
							// Test semester change
							let changeResult: any;
							await act(async () => {
								changeResult = await result.current.changeSemester(alternativeSemester.value, {
									semesters,
									mainForm: loginResult.data.mainForm,
									signInToken: loginResult.data.signInToken || 'mock-token'
								});
							});

							expect(changeResult).toBeDefined();
							console.log('✅ Semester change successful');
							console.log('Changed to:', alternativeSemester.value);
						} else {
							console.log('ℹ️ Only one semester available, skipping semester change test');
						}
					} else {
						console.log('ℹ️ Only one semester available, skipping semester change test');
					}
				}
			} catch (error) {
				console.error('❌ Semester change failed:', error);
				throw error;
			}
		}, 90000);
	});

	describe('Logout Functionality', () => {
		it('should logout successfully after login', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			const credentials = getTestCredentials();
			jest.setTimeout(60000);

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			try {
				// First login
				await act(async () => {
					await result.current.loginWithCredentials(credentials.username, credentials.password);
				});

				// Then logout
				await act(async () => {
					result.current.logout();
				});

				console.log('✅ Logout successful');
			} catch (error) {
				console.error('❌ Logout failed:', error);
				throw error;
			}
		}, 60000);
	});

	describe('Error Handling', () => {
		it('should handle processing state correctly', async () => {
			if (skipIfNoRealAccount()) {
				console.log('Skipping real account test - no credentials configured');
				return;
			}

			const credentials = getTestCredentials();
			jest.setTimeout(30000);

			const { result } = renderHook(() => useCalendarData(), { wrapper });

			expect(result.current.isProcessing).toBe(false);

			// Start login process
			const loginPromise = act(async () => {
				return result.current.loginWithCredentials(credentials.username, credentials.password);
			});

			// Processing state should be handled by the auth context
			// We can't directly test isProcessing here as it's managed by the context

			await loginPromise;

			console.log('✅ Processing state handled correctly');
		}, 30000);
	});
});
